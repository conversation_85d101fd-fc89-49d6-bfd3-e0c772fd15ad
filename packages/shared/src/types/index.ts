// Export all types

export * from './api'
export * from './user'

// Common utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// Route types
export interface RouteConfig {
  path: string
  component: React.ComponentType
  requiresAuth?: boolean
  allowedRoles?: string[]
  title?: string
  description?: string
}
