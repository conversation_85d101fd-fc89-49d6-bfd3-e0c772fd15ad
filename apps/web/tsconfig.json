{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@ui": ["../../packages/ui/src"],
      "@ui/*": ["../../packages/ui/src/*"],
      "@shared": ["../../packages/shared/src"],
      "@shared/*": ["../../packages/shared/src/*"],
      "@owner": ["../../packages/owner/src"],
      "@owner/*": ["../../packages/owner/src/*"],
      "@contractor": ["../../packages/contractor/src"],
      "@contractor/*": ["../../packages/contractor/src/*"],
      "@admin": ["../../packages/admin/src"],
      "@admin/*": ["../../packages/admin/src/*"]
    },

    /* Monorepo TypeScript references */
    "composite": true,
    "declaration": true,
    "declarationMap": true,
    "outDir": "./dist"
  },
  "include": ["src/**/*", "vite-env.d.ts"],
  "references": [
    { "path": "../../packages/ui" },
    { "path": "../../packages/shared" },
    { "path": "../../packages/owner" },
    { "path": "../../packages/contractor" },
    { "path": "../../packages/admin" }
  ]
}
